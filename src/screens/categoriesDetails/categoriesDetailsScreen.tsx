import React, {useState, useLayoutEffect, useEffect, useCallback} from 'react';
import {
  StyleSheet,
  FlatList,
  SafeAreaView,
  View,
  ActivityIndicator,
  TouchableOpacity,
  Keyboard,
} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {Text} from 'react-native-paper';
import {useFocusEffect} from '@react-navigation/native';
import axios from 'axios';
import {NativeAd} from 'react-native-google-mobile-ads';
import CustomCategoriesDetailCard from '../../components/CustomCategoriesDetailCard';
import CustomSearchBar from '../../components/CustomSearchBar';
import {CONFIG} from '../../common/constant';
import commonStyles from '../../common/commonStyles';
import {showErrorToast} from '../../utils/showToast.js';
// Add these imports
import watermelonCompanyRepository from '../../database/watermelon/repositories/companyRepository';
import watermelonCompanyCategoryRepository from '../../database/watermelon/repositories/companyCategoryRepository';
import syncStateRepository from '../../database/watermelon/repositories/syncStateRepository';
import {UICompany} from '../../types/company';
import {
  convertDBCompaniesToUI,
  convertApiCompaniesToUI,
} from '../../utils/companyConverter';
import DebugSync from '../../utils/debugSync';
import {useBankHolidayForCategory} from '../../hooks/useBankHoliday';
import BankHolidayBanner from '../../components/BankHolidayBanner';
import {useNetworkState} from '../../utils/networkStateManager';
import CustomNativeAdCard from '../../components/ads/CustomNativeAdCard';
import adMobService from '../../services/adMobService';

const CompanyScreen = ({route, navigation}: {route: any; navigation: any}) => {
  // Get safe area insets for proper bottom padding
  const insets = useSafeAreaInsets();

  // Add network connectivity state
  const {isConnected} = useNetworkState();

  // Add state for sync status with caching
  const [isFullySynced, setIsFullySynced] = useState(false);
  const [syncStatusCache, setSyncStatusCache] = useState<{
    timestamp: number;
    fullySync: boolean;
    companySyncState: any;
    companyCategorySyncState: any;
  } | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [page, setPage] = useState(1);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [isScreenFocused, setIsScreenFocused] = useState(false);

  // Native ad state
  const [nativeAd, setNativeAd] = useState<NativeAd | null>(null);
  const [nativeAdHeight, setNativeAdHeight] = useState(0);
  const [adLoadingAttempts, setAdLoadingAttempts] = useState(0);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);

  const {title, categoryId = 50} = route.params;
  const [loading, setLoading] = useState(true); // Start with true to show loading immediately
  const [companies, setCompanies] = useState<UICompany[]>([]);

  // Use bank holiday hook for category
  const {isBankCategory, bankHolidayMessage} =
    useBankHolidayForCategory(categoryId);

  // Check if data is fully synced (optimized with caching)
  const checkSyncStatus = async (useCache = true) => {
    try {
      // Check cache first (valid for 5 seconds)
      const now = Date.now();
      if (
        useCache &&
        syncStatusCache &&
        now - syncStatusCache.timestamp < 5000
      ) {
        return {
          fullySync: syncStatusCache.fullySync,
          companySyncState: syncStatusCache.companySyncState,
          companyCategorySyncState: syncStatusCache.companyCategorySyncState,
        };
      }

      // Get sync states for companies and company_categories in parallel
      const [companySyncState, companyCategorySyncState] = await Promise.all([
        syncStateRepository.getBySyncKey('companies'),
        syncStateRepository.getBySyncKey('company_categories'),
      ]);

      // Check if both are fully synced (progress = 100 AND status = 'completed')
      const isCompanySynced =
        companySyncState &&
        companySyncState.progress === 100 &&
        companySyncState.status === 'completed';
      const isCompanyCategorySynced =
        companyCategorySyncState &&
        companyCategorySyncState.progress === 100 &&
        companyCategorySyncState.status === 'completed';

      // Both must be synced to use local data
      const fullySync = !!(isCompanySynced && isCompanyCategorySynced);
      setIsFullySynced(fullySync);

      // Cache the result
      setSyncStatusCache({
        timestamp: now,
        fullySync,
        companySyncState,
        companyCategorySyncState,
      });

      return {
        fullySync,
        companySyncState,
        companyCategorySyncState,
      };
    } catch (error) {
      console.error('[CompanyScreen] Error checking sync status:', error);
      return {
        fullySync: false,
        companySyncState: null,
        companyCategorySyncState: null,
      };
    }
  };

  // Fetch companies from local database (includes local search functionality)
  const fetchCompaniesFromLocalDB = async (
    searchQuery = '',
    showLoading = true,
  ) => {
    try {
      // Only show loading indicators if explicitly requested
      if (showLoading) {
        if (searchQuery && searchQuery.trim() !== '') {
          setIsSearching(false);
        } else {
          setLoading(true);
        }
      }
      // 1. Get company-category relationships for this category
      const companyCategories =
        await watermelonCompanyCategoryRepository.getByCategoryId(categoryId);

      if (companyCategories.length === 0) {
        setCompanies([]);
        setHasMoreData(false);
        return;
      }

      // 2. Extract company IDs
      const companyIds = companyCategories.map(cc => cc.companyId);

      // 3. Get companies by their IDs using batch query (much faster than individual queries)
      const localCompanies = await watermelonCompanyRepository.getByCompanyIds(
        companyIds,
      );

      // 4. Apply search filter locally (no API call needed)
      let filteredCompanies = localCompanies;
      if (searchQuery && searchQuery.trim()) {
        const trimmedQuery = searchQuery.trim().toLowerCase();
        filteredCompanies = localCompanies.filter(company =>
          company.company_name.toLowerCase().trim().includes(trimmedQuery),
        );
      }

      // 5. Sort companies by priority (lower values first) and then alphabetically
      const sortedCompanies = filteredCompanies.sort((a, b) => {
        // First sort by priority (lower values appear first)
        const priorityDiff =
          (a.company_priority || 999999) - (b.company_priority || 999999);
        if (priorityDiff !== 0) {
          return priorityDiff;
        }
        // If priorities are equal, sort alphabetically by company name
        return a.company_name.localeCompare(b.company_name);
      });

      // 6. Convert to the format expected by the UI using the converter utility
      const formattedCompanies = convertDBCompaniesToUI(sortedCompanies);

      setCompanies(formattedCompanies);
      setHasMoreData(false); // No pagination for local data
      setError(null);
    } catch (err: any) {
      console.error('[CompanyScreen] Error fetching from local DB:', err);
      setError('Failed to load companies from local database');
      // Fall back to API if local fetch fails
      fetchCompaniesFromAPI(1, searchQuery, true);
    } finally {
      setLoading(false);
      setRefreshing(false);
      setIsSearching(false);
    }
  };

  const buildApiUrl = (
    pageNum: number,
    searchQuery: string,
    limit: number = 40,
  ) => {
    return `${
      CONFIG.API_URL
    }/company?categoryId=${categoryId}&page=${pageNum}&limit=${limit}&sortBy=created_at&sortOrder=DESC${
      searchQuery ? `&search=${searchQuery}` : ''
    }`;
  };

  // Rename to fetchCompaniesFromAPI for clarity
  const fetchCompaniesFromAPI = async (
    pageNum = 1,
    searchQuery = '',
    shouldReset = false,
    showLoading = true,
  ) => {
    try {
      // Only show loading indicators if explicitly requested
      if (showLoading) {
        if (shouldReset && searchQuery && searchQuery.trim() !== '') {
          setIsSearching(true);
        } else {
          setLoading(true);
        }
      }
      const apiUrl = buildApiUrl(pageNum, searchQuery);

      const response = await axios.get(apiUrl, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          // Add authorization header if needed
          // 'Authorization': 'Bearer YOUR_TOKEN'
        },
        timeout: 50000, // 50 seconds timeout
      });

      // Check if we received data and it was successful
      if (
        response.data &&
        response.data.success &&
        response.data.data &&
        response.data.data.companies
      ) {
        // Convert API companies to UI format before using them
        const apiCompanies = response.data.data.companies;
        const receivedCompanies = convertApiCompaniesToUI(apiCompanies);

        // Sort received companies by priority (lower values first) and then alphabetically
        const sortedReceivedCompanies = receivedCompanies.sort((a, b) => {
          // First sort by priority (lower values appear first)
          const priorityDiff =
            (a.companyPriority || 999999) - (b.companyPriority || 999999);
          if (priorityDiff !== 0) {
            return priorityDiff;
          }
          // If priorities are equal, sort alphabetically by company name
          return a.companyName.localeCompare(b.companyName);
        });

        // If shouldReset is true, replace the companies array
        // Otherwise append to existing companies (for pagination)
        let newCompanies = shouldReset
          ? sortedReceivedCompanies
          : [...companies, ...sortedReceivedCompanies];

        // Sort the entire list again to maintain proper priority order across pages
        if (!shouldReset && newCompanies.length > 0) {
          newCompanies = newCompanies.sort((a, b) => {
            // First sort by priority (lower values appear first)
            const priorityDiff =
              (a.companyPriority || 999999) - (b.companyPriority || 999999);
            if (priorityDiff !== 0) {
              return priorityDiff;
            }
            // If priorities are equal, sort alphabetically by company name
            return a.companyName.localeCompare(b.companyName);
          });
        }

        setCompanies(newCompanies);

        // Check if we've reached the end of the data
        // We check if the length of companies received is less than the limit (20)
        // or if we've reached the total number of companies
        const totalCompanies = response.data.data.total || 0;
        const hasMoreToLoad =
          receivedCompanies.length === 20 &&
          (shouldReset
            ? receivedCompanies.length
            : companies.length + receivedCompanies.length) < totalCompanies;
        setHasMoreData(hasMoreToLoad);

        // Clear any previous errors
        setError(null);
      } else {
        setHasMoreData(false);
      }
    } catch (err: any) {
      console.error('API Error:', err);

      // Handle Axios errors
      if (err.response) {
        // Server responded with error status
        console.error('Error response data:', err.response.data);
        console.error('Error response status:', err.response.status);
        const errorMessage =
          err.response.data?.message || `Server error: ${err.response.status}`;
        showErrorToast(new Error(errorMessage));
        setError(errorMessage);
      } else if (err.request) {
        // Request was made but no response received
        console.error('No response received:', err.request);
        const errorMessage = 'Network error: No response from server';
        showErrorToast(new Error(errorMessage));
        setError(errorMessage);
      } else {
        // Something else happened
        console.error('Error message:', err.message);
        showErrorToast(err);
        setError(err.message || 'Failed to fetch companies');
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
      setIsSearching(false);
    }
  };

  // Main fetch function that decides whether to use local or API data
  const fetchCompanies = async (
    pageNum = 1,
    searchQuery = '',
    shouldReset = false,
    skipSyncCheck = false,
  ) => {
    // Use cached sync status if available to avoid repeated checks
    let fullySync = isFullySynced;
    let companySyncState = null;
    let companyCategorySyncState = null;

    // Only check sync status if not skipped and not already known to be synced
    if (!skipSyncCheck && !fullySync) {
      // Use cached result if available (much faster)
      const syncResult = await checkSyncStatus(true);
      fullySync = syncResult.fullySync;
      companySyncState = syncResult.companySyncState;
      companyCategorySyncState = syncResult.companyCategorySyncState;
    }

    // If data is fully synced, use local database for both regular fetching and searching
    if (fullySync) {
      fetchCompaniesFromLocalDB(searchQuery, true); // Show loading for user-initiated actions
      return;
    }

    // NEW LOGIC: If sync is not completed AND device has internet, call specific API
    if (!fullySync && isConnected) {
      const companySyncNotCompleted =
        !companySyncState || companySyncState.status !== 'completed';
      const companyCategorySyncNotCompleted =
        !companyCategorySyncState ||
        companyCategorySyncState.status !== 'completed';

      if (companySyncNotCompleted || companyCategorySyncNotCompleted) {
        // Use existing API function with specific parameters: page=1, limit=40, with search
        fetchCompaniesFromAPI(pageNum, searchQuery, shouldReset);
        return;
      }
    }

    // If not fully synced, check if sync is currently in progress
    const isCompanySyncInProgress = companySyncState?.status === 'in_progress';
    const isCompanyCategorySyncInProgress =
      companyCategorySyncState?.status === 'in_progress';

    // If sync is in progress, avoid using local data as it might be inconsistent
    if (isCompanySyncInProgress || isCompanyCategorySyncInProgress) {
      fetchCompaniesFromAPI(pageNum, searchQuery, shouldReset);
      return;
    }

    // If sync is not in progress, check if we have stable local data
    const companyCount = await watermelonCompanyRepository.getCount();
    const companyCategoryCount =
      await watermelonCompanyCategoryRepository.getCount();

    if (companyCount > 0 && companyCategoryCount > 0) {
      try {
        await fetchCompaniesFromLocalDB(searchQuery, true); // Show loading for fallback scenarios
        return; // If local fetch succeeds, we're done
      } catch (error) {
        console.warn(
          '[CompanyScreen] Local data fetch failed, falling back to API:',
          error,
        );
      }
    }

    // Fall back to API if no local data or local fetch failed
    fetchCompaniesFromAPI(pageNum, searchQuery, shouldReset);
  };

  // Handle search
  const handleSearch = (query: string) => {
    setPage(1);
    setSearchQuery(query);
    // Skip sync check for search as we want immediate response
    fetchCompanies(1, query, true, true);
  };

  // Debug function to check and fix stuck syncs
  const debugStuckSyncs = async () => {
    try {
      await DebugSync.autoFixStuckSyncs();
    } catch (error) {
      console.error('[CompanyScreen] Debug sync error:', error);
    }
  };

  // Reset native ad height when ad is removed
  useEffect(() => {
    if (!nativeAd) {
      setNativeAdHeight(0);
    }
  }, [nativeAd]);

  // Keyboard event listeners
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setIsKeyboardVisible(true);
      },
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setIsKeyboardVisible(false);
      },
    );

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, []);

  // Initial data fetch on component mount
  useEffect(() => {
    // Initialize native ad with retry logic
    const loadNativeAd = async () => {
      try {
        setAdLoadingAttempts(prev => prev + 1);
        console.log(
          `[CompanyScreen] Attempting to load native ad... (attempt ${
            adLoadingAttempts + 1
          })`,
        );
        const ad = await adMobService.initiateNativeAd();
        if (ad) {
          console.log('[CompanyScreen] Native ad loaded successfully');
          setNativeAd(ad);
        } else {
          console.log(
            '[CompanyScreen] No native ad returned, retrying in 3 seconds...',
          );
          // Retry after 3 seconds (max 3 attempts)
          if (adLoadingAttempts < 2) {
            setTimeout(async () => {
              try {
                setAdLoadingAttempts(prev => prev + 1);
                const retryAd = await adMobService.initiateNativeAd();
                if (retryAd) {
                  console.log('[CompanyScreen] Native ad loaded on retry');
                  setNativeAd(retryAd);
                } else {
                  console.log('[CompanyScreen] Native ad retry failed');
                }
              } catch (retryError) {
                console.error(
                  '[CompanyScreen] Native ad retry error:',
                  retryError,
                );
              }
            }, 3000);
          } else {
            console.log(
              '[CompanyScreen] Max retry attempts reached for native ad',
            );
          }
        }
      } catch (error) {
        console.error('[CompanyScreen] Failed to load native ad:', error);
        // Retry after 5 seconds on error (max 3 attempts)
        if (adLoadingAttempts < 2) {
          setTimeout(async () => {
            try {
              console.log('[CompanyScreen] Retrying native ad after error...');
              setAdLoadingAttempts(prev => prev + 1);
              const retryAd = await adMobService.initiateNativeAd();
              if (retryAd) {
                console.log(
                  '[CompanyScreen] Native ad loaded after error retry',
                );
                setNativeAd(retryAd);
              }
            } catch (retryError) {
              console.error(
                '[CompanyScreen] Native ad error retry failed:',
                retryError,
              );
            }
          }, 5000);
        } else {
          console.log(
            '[CompanyScreen] Max error retry attempts reached for native ad',
          );
        }
      }
    };

    loadNativeAd();

    // Start loading data immediately with parallel sync check
    const initializeScreen = async () => {
      // Start background processes (non-blocking)
      debugStuckSyncs(); // Run in background

      // Check sync status immediately in parallel with data loading
      const syncCheckPromise = checkSyncStatus();

      try {
        // Start both sync check and local data loading in parallel
        const [syncResult] = await Promise.all([
          syncCheckPromise,
          // Try local data first (but don't wait if sync is incomplete)
          fetchCompaniesFromLocalDB('', true).catch(() => {
            console.warn('[CompanyScreen] Local data not available');
            return null;
          }),
        ]);

        // If sync is incomplete and we have internet, call API immediately
        if (!syncResult.fullySync && isConnected) {
          console.log(
            '[CompanyScreen] Sync incomplete, calling API immediately',
          );
          // Call API with loading indicator (this will replace local data if any)
          await fetchCompaniesFromAPI(1, '', true, true);
        }
      } catch (error) {
        // If everything fails, fall back to API
        console.warn(
          '[CompanyScreen] All data loading failed, using API fallback',
        );
        fetchCompaniesFromAPI(1, '', true, true);
      }
    };

    initializeScreen();
  }, [categoryId]);

  // Listen for sync completion to automatically switch to local data
  useEffect(() => {
    let syncCheckInterval: NodeJS.Timeout;
    let consecutiveCompletedChecks = 0;

    const checkForSyncCompletion = async () => {
      if (isFullySynced) {
        // Already using local data, no need to check
        return;
      }

      const syncResult = await checkSyncStatus();
      const {fullySync} = syncResult;

      if (fullySync && !isFullySynced) {
        // Refresh data to use local database
        // Only pass searchQuery if it's not empty to avoid triggering search loading
        const queryToUse = searchQuery.trim() === '' ? '' : searchQuery;
        fetchCompanies(1, queryToUse, true);
        consecutiveCompletedChecks = 0;
      } else if (fullySync) {
        // If already synced, reduce check frequency
        consecutiveCompletedChecks++;
        if (consecutiveCompletedChecks >= 3) {
          // Stop checking after 3 consecutive completed checks
          if (syncCheckInterval) {
            clearInterval(syncCheckInterval);
          }
          return;
        }
      } else {
        consecutiveCompletedChecks = 0;
      }
    };

    // Check every 10 seconds for sync completion (optimized for performance)
    syncCheckInterval = setInterval(checkForSyncCompletion, 10000);

    // Cleanup interval on unmount
    return () => {
      if (syncCheckInterval) {
        clearInterval(syncCheckInterval);
      }
    };
  }, [isFullySynced, searchQuery]);

  // Load more data when reaching the end of the list
  const loadMoreCompanies = () => {
    // Only load more if using API data (not local)
    // Local data loads all results at once, so no pagination needed
    if (!loading && hasMoreData && !isFullySynced) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchCompanies(nextPage, searchQuery);
    }
  };

  // Handle refresh
  const handleRefresh = () => {
    setRefreshing(true);
    setPage(1);
    fetchCompanies(1, searchQuery, true);
  };

  useLayoutEffect(() => {
    navigation.setOptions({title});
  }, [navigation, title]);

  // Reset search state when screen comes back into focus (e.g., navigating back from CompanyDetailsScreen)
  useFocusEffect(
    useCallback(() => {
      // Reset search-related states when screen comes into focus
      setIsSearching(false);
      setIsScreenFocused(true);

      // Try to load native ad if not already loaded
      if (!nativeAd) {
        console.log(
          '[CompanyScreen] Screen focused, attempting to load native ad...',
        );
        const loadAdOnFocus = async () => {
          try {
            const ad = await adMobService.initiateNativeAd();
            if (ad) {
              console.log('[CompanyScreen] Native ad loaded on screen focus');
              setNativeAd(ad);
            } else {
              console.log('[CompanyScreen] No native ad available on focus');
            }
          } catch (error) {
            console.error(
              '[CompanyScreen] Failed to load native ad on focus:',
              error,
            );
          }
        };
        loadAdOnFocus();
      }

      // Only reset search query if it's not empty (to preserve user's search)
      // But ensure isSearching is false to prevent loading indicators
      return () => {
        // Cleanup function when screen loses focus
        setIsScreenFocused(false);
      };
    }, [nativeAd]),
  );

  // Render loading indicator
  const renderLoadingIndicator = () => {
    // Show loading indicator for initial loads, not for refreshing
    if (!loading || refreshing) return null;

    return (
      <View style={commonStyles.loadingContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
        <Text style={commonStyles.loadingText}>Loading companies...</Text>
      </View>
    );
  };

  // Render empty list message
  const renderEmptyList = () => {
    // Show loading indicator for main loading (not refreshing)
    if (loading && !refreshing) return renderLoadingIndicator();

    return (
      <View style={commonStyles.emptyContainer}>
        <Text style={commonStyles.emptyText}>
          {searchQuery
            ? 'No companies match your search.'
            : 'No companies found in this category.'}
        </Text>
      </View>
    );
  };

  // Use UICompany type for consistency
  type Company = UICompany;

  // Render a single company item
  const renderCompanyItem = ({item}: {item: Company}) => (
    <CustomCategoriesDetailCard companyData={item} categoryId={categoryId} />
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Show Bank Holidays label only for Bank category (categoryId = 2) and on specific holidays */}
      <BankHolidayBanner
        isVisible={isBankCategory}
        message={bankHolidayMessage}
      />
      {/* Temporarily hidden complaint information
      <View style={{paddingLeft: 15, paddingRight: 15}}>
        <Text
          style={[
            commonStyles.instructionText,
            commonStyles.instructionTextTopSpace,
          ]}>
          For Cyber Fraud Complaint: 1930
        </Text>
        <Text
          style={[
            commonStyles.instructionText,
            commonStyles.instructionTextBottomSpace,
          ]}>
          For Complaint Through RBI: 14440
        </Text>
      </View>
      */}
      <View style={{marginTop: 0}} />
      <CustomSearchBar
        onSearch={handleSearch}
        onVoiceResult={result => {
          handleSearch(result);
        }}
        isSearching={isSearching}
        initialValue={searchQuery}
        placeholder="Search companies"
        showVoiceSearch={true}
        screenName="CompanyScreen"
        isScreenFocused={isScreenFocused}
      />

      {/* Add to Home Screen shortcut button 
      {isShortcutSupported && (
        <View style={{paddingHorizontal: 15, paddingTop: 10}}>
          <ShortcutButton
            title={`Add "${title}" to Home Screen`}
            onPress={() => createCompanyListShortcut(categoryId, title)}
            isLoading={shortcutLoading}
          />
        </View>
      )}*/}

      <View style={{flex: 1}}>
        {/* Error display */}
        {error && (
          <View style={commonStyles.errorContainer}>
            <Text style={commonStyles.errorText}>{error}</Text>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={() => fetchCompanies(1, searchQuery, true)}>
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        )}
        <FlatList
          style={{padding: 15}}
          data={companies}
          extraData={companies}
          keyExtractor={(item, index) =>
            `${item.companyId.toString()}-${index}`
          }
          renderItem={renderCompanyItem}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          onEndReached={loadMoreCompanies}
          onEndReachedThreshold={0.3}
          ListEmptyComponent={renderEmptyList}
          refreshing={refreshing}
          onRefresh={handleRefresh}
          contentContainerStyle={
            companies.length === 0
              ? commonStyles.fullHeight
              : {
                  paddingBottom: Math.max(
                    insets.bottom +
                      (nativeAd && !isKeyboardVisible
                        ? nativeAdHeight > 0
                          ? nativeAdHeight
                          : 190
                        : 5),
                    20,
                  ), // Dynamic padding based on actual ad height with fallback, no padding when keyboard is visible
                }
          }
        />
      </View>

      {/* Native Ad - Sticky at bottom, hidden when keyboard is visible */}
      {nativeAd && !isKeyboardVisible && (
        <View
          style={commonStyles.nativeAdContainer}
          onLayout={event => {
            const {height} = event.nativeEvent.layout;
            setNativeAdHeight(height);
            console.log('[CompanyScreen] Native ad height measured:', height);
          }}>
          <CustomNativeAdCard ad={nativeAd} />
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  retryButton: {
    backgroundColor: '#ef5350',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: '600',
  },
});

export default CompanyScreen;
